'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon, 
  CreditCardIcon, 
  CalendarIcon, 
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon,
  BanknotesIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'

interface Payment {
  id: number
  amount: number
  paymentmethod: string
  status: string
  paymentdate?: string
  transactionid?: string
  description?: string
  invoiceid?: number
}

interface PaymentViewModalProps {
  isOpen: boolean
  onClose: () => void
  payment: Payment
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'success':
    case 'paid':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
    case 'processing':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'failed':
    case 'declined':
    case 'error':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'cancelled':
    case 'refunded':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
    case 'success':
    case 'paid':
      return CheckCircleIcon
    case 'pending':
    case 'processing':
      return ClockIcon
    case 'failed':
    case 'declined':
    case 'error':
      return ExclamationTriangleIcon
    case 'cancelled':
    case 'refunded':
      return XMarkIcon
    default:
      return ClockIcon
  }
}

const getPaymentMethodIcon = (method: string) => {
  switch (method?.toLowerCase()) {
    case 'credit card':
    case 'debit card':
    case 'card':
    case 'stripe':
      return CreditCardIcon
    case 'bank transfer':
    case 'wire transfer':
    case 'ach':
      return BanknotesIcon
    case 'paypal':
      return CurrencyDollarIcon
    default:
      return CreditCardIcon
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatPaymentMethod = (method: string) => {
  return method.split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

export default function PaymentViewModal({
  isOpen,
  onClose,
  payment
}: PaymentViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(payment.status)
  const PaymentMethodIcon = getPaymentMethodIcon(payment.paymentmethod)

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-white">Payment Details</h2>
                  <p className="text-green-100">Payment #{payment.id}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(payment.status)}`}>
                    <StatusIcon className="h-4 w-4 inline mr-1" />
                    {payment.status}
                  </div>
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Payment Summary */}
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-6 mb-8">
                <div className="text-center">
                  <div className="flex justify-center mb-4">
                    <div className={`p-3 rounded-full ${getStatusColor(payment.status).replace('border-', 'bg-').replace('text-', 'text-').replace('100', '200')}`}>
                      <StatusIcon className="h-8 w-8" />
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {formatCurrency(payment.amount)}
                  </h3>
                  <p className="text-lg text-gray-600">
                    Payment {payment.status?.toLowerCase() === 'completed' || payment.status?.toLowerCase() === 'success' ? 'Successful' : payment.status}
                  </p>
                </div>
              </div>

              {/* Payment Information Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Payment Details */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Payment ID</p>
                        <p className="text-sm text-gray-600">#{payment.id}</p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <PaymentMethodIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Payment Method</p>
                        <p className="text-sm text-gray-600">{formatPaymentMethod(payment.paymentmethod)}</p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Amount</p>
                        <p className="text-sm text-gray-600 font-semibold">{formatCurrency(payment.amount)}</p>
                      </div>
                    </div>

                    {payment.paymentdate && (
                      <div className="flex items-center">
                        <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Payment Date</p>
                          <p className="text-sm text-gray-600">{formatDate(payment.paymentdate)}</p>
                        </div>
                      </div>
                    )}

                    {payment.invoiceid && (
                      <div className="flex items-center">
                        <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Related Invoice</p>
                          <p className="text-sm text-blue-600 font-medium">Invoice #{payment.invoiceid}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Transaction Details */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Transaction Details</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    <div className="flex items-center">
                      <CheckCircleIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Status</p>
                        <p className={`text-sm font-medium ${
                          payment.status?.toLowerCase() === 'completed' || payment.status?.toLowerCase() === 'success' 
                            ? 'text-green-600' 
                            : payment.status?.toLowerCase() === 'failed' || payment.status?.toLowerCase() === 'declined'
                            ? 'text-red-600'
                            : 'text-yellow-600'
                        }`}>
                          {payment.status}
                        </p>
                      </div>
                    </div>

                    {payment.transactionid && (
                      <div className="flex items-center">
                        <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Transaction ID</p>
                          <p className="text-sm text-gray-600 font-mono break-all">{payment.transactionid}</p>
                        </div>
                      </div>
                    )}

                    {!payment.transactionid && (
                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500">No additional transaction details available</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              {payment.description && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {payment.description}
                    </p>
                  </div>
                </div>
              )}

              {/* Status Information */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Status</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <StatusIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Current Status</p>
                        <p className="text-sm text-gray-600">{payment.status}</p>
                      </div>
                    </div>
                    <div className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(payment.status)}`}>
                      {payment.status}
                    </div>
                  </div>

                  {(payment.status?.toLowerCase() === 'completed' || payment.status?.toLowerCase() === 'success') && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                        <p className="text-sm text-green-700 font-medium">
                          Payment processed successfully
                        </p>
                      </div>
                    </div>
                  )}

                  {(payment.status?.toLowerCase() === 'failed' || payment.status?.toLowerCase() === 'declined') && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
                        <p className="text-sm text-red-700 font-medium">
                          Payment failed to process
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t">
                <div className="text-sm text-gray-500">
                  Payment #{payment.id}
                </div>
                <div className="flex space-x-3">
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download Receipt
                  </button>
                  {payment.invoiceid && (
                    <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      <DocumentTextIcon className="h-4 w-4 mr-2" />
                      View Invoice
                    </button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
