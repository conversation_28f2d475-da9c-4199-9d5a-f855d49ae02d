'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon, 
  DocumentTextIcon, 
  CalendarIcon, 
  CurrencyDollarIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PaperAirplaneIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'

interface QuoteRequest {
  id: number
  servicetype: string
  description: string
  budget?: number
  timeline?: string
  status: string
  requestdate?: string
  responsedate?: string
  quotedamount?: number
  estimatedtime?: string
  notes?: string
}

interface QuotationViewModalProps {
  isOpen: boolean
  onClose: () => void
  quotation: QuoteRequest
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'approved':
    case 'accepted':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
    case 'submitted':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'rejected':
    case 'declined':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'quoted':
    case 'responded':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'approved':
    case 'accepted':
      return CheckCircleIcon
    case 'pending':
    case 'submitted':
      return ClockIcon
    case 'rejected':
    case 'declined':
      return ExclamationTriangleIcon
    case 'draft':
      return DocumentTextIcon
    case 'quoted':
    case 'responded':
      return PaperAirplaneIcon
    default:
      return ClockIcon
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const formatServiceType = (serviceType: string) => {
  return serviceType.split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

export default function QuotationViewModal({
  isOpen,
  onClose,
  quotation
}: QuotationViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(quotation.status)

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-600 to-purple-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-white">Quotation Details</h2>
                  <p className="text-purple-100">Quote Request #{quotation.id}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(quotation.status)}`}>
                    <StatusIcon className="h-4 w-4 inline mr-1" />
                    {quotation.status}
                  </div>
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Request Information Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Request Details */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Request Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Request ID</p>
                        <p className="text-sm text-gray-600">#{quotation.id}</p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <PaperAirplaneIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Service Type</p>
                        <p className="text-sm text-gray-600">{formatServiceType(quotation.servicetype)}</p>
                      </div>
                    </div>

                    {quotation.requestdate && (
                      <div className="flex items-center">
                        <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Request Date</p>
                          <p className="text-sm text-gray-600">{formatDate(quotation.requestdate)}</p>
                        </div>
                      </div>
                    )}

                    {quotation.timeline && (
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Requested Timeline</p>
                          <p className="text-sm text-gray-600">{quotation.timeline}</p>
                        </div>
                      </div>
                    )}

                    {quotation.budget && (
                      <div className="flex items-center">
                        <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Budget Range</p>
                          <p className="text-sm text-gray-600">{formatCurrency(quotation.budget)}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Quote Response */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Quote Response</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    {quotation.responsedate ? (
                      <>
                        <div className="flex items-center">
                          <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <p className="text-sm font-medium text-gray-900">Response Date</p>
                            <p className="text-sm text-gray-600">{formatDate(quotation.responsedate)}</p>
                          </div>
                        </div>

                        {quotation.quotedamount && (
                          <div className="flex items-center">
                            <CurrencyDollarIcon className="h-5 w-5 text-green-500 mr-3" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">Quoted Amount</p>
                              <p className="text-sm text-green-600 font-semibold text-lg">
                                {formatCurrency(quotation.quotedamount)}
                              </p>
                            </div>
                          </div>
                        )}

                        {quotation.estimatedtime && (
                          <div className="flex items-center">
                            <ClockIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div>
                              <p className="text-sm font-medium text-gray-900">Estimated Time</p>
                              <p className="text-sm text-gray-600">{quotation.estimatedtime}</p>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="text-center py-8">
                        <ClockIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                        <p className="text-sm text-gray-500">Awaiting response</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Description</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {quotation.description}
                  </p>
                </div>
              </div>

              {/* Notes */}
              {quotation.notes && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Notes</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {quotation.notes}
                    </p>
                  </div>
                </div>
              )}

              {/* Status Information */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Information</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <StatusIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Current Status</p>
                        <p className="text-sm text-gray-600">{quotation.status}</p>
                      </div>
                    </div>
                    <div className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(quotation.status)}`}>
                      {quotation.status}
                    </div>
                  </div>

                  {quotation.status?.toLowerCase() === 'approved' && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                      <div className="flex items-center">
                        <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                        <p className="text-sm text-green-700 font-medium">
                          Quote has been approved and project can begin
                        </p>
                      </div>
                    </div>
                  )}

                  {quotation.status?.toLowerCase() === 'rejected' && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
                        <p className="text-sm text-red-700 font-medium">
                          Quote has been declined
                        </p>
                      </div>
                    </div>
                  )}

                  {quotation.status?.toLowerCase() === 'pending' && (
                    <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <div className="flex items-center">
                        <ClockIcon className="h-5 w-5 text-yellow-500 mr-2" />
                        <p className="text-sm text-yellow-700 font-medium">
                          Quote is under review
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t">
                <div className="text-sm text-gray-500">
                  Quote Request #{quotation.id}
                </div>
                <div className="flex space-x-3">
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download Quote
                  </button>
                  {quotation.status?.toLowerCase() === 'quoted' && (
                    <>
                      <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <XMarkIcon className="h-4 w-4 mr-2" />
                        Decline
                      </button>
                      <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <CheckCircleIcon className="h-4 w-4 mr-2" />
                        Accept Quote
                      </button>
                    </>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
