'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon, 
  DocumentTextIcon, 
  CalendarIcon, 
  CurrencyDollarIcon,
  ArrowDownTrayIcon,
  CheckIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Invoice {
  id: number
  totalamount: number
  subtotal?: number
  taxrate?: number
  taxamount?: number
  status: string
  issuedate?: string
  duedate?: string
  paidat?: string
  description?: string
}

interface InvoiceViewModalProps {
  isOpen: boolean
  onClose: () => void
  invoice: Invoice
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'paid':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'overdue':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'draft':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'paid':
      return CheckIcon
    case 'pending':
      return ClockIcon
    case 'overdue':
      return ExclamationTriangleIcon
    case 'draft':
      return DocumentTextIcon
    case 'cancelled':
      return XMarkIcon
    default:
      return ClockIcon
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getDaysUntilDue = (dueDate: string) => {
  const today = new Date()
  const due = new Date(dueDate)
  const diffTime = due.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export default function InvoiceViewModal({
  isOpen,
  onClose,
  invoice
}: InvoiceViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(invoice.status)
  const daysUntilDue = invoice.duedate ? getDaysUntilDue(invoice.duedate) : null
  const isOverdue = daysUntilDue !== null && daysUntilDue < 0 && invoice.status?.toLowerCase() !== 'paid'

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-white">Invoice Details</h2>
                  <p className="text-blue-100">Invoice #{invoice.id}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(invoice.status)}`}>
                    <StatusIcon className="h-4 w-4 inline mr-1" />
                    {invoice.status}
                  </div>
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Invoice Header */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Invoice Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoice Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    <div className="flex items-center">
                      <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Invoice Number</p>
                        <p className="text-sm text-gray-600">#{invoice.id}</p>
                      </div>
                    </div>

                    {invoice.issuedate && (
                      <div className="flex items-center">
                        <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Issue Date</p>
                          <p className="text-sm text-gray-600">{formatDate(invoice.issuedate)}</p>
                        </div>
                      </div>
                    )}

                    {invoice.duedate && (
                      <div className="flex items-center">
                        <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Due Date</p>
                          <p className={`text-sm ${isOverdue ? 'text-red-600 font-medium' : 'text-gray-600'}`}>
                            {formatDate(invoice.duedate)}
                            {isOverdue && daysUntilDue && (
                              <span className="ml-1 text-red-500">
                                ({Math.abs(daysUntilDue)} days overdue)
                              </span>
                            )}
                          </p>
                        </div>
                      </div>
                    )}

                    {invoice.status?.toLowerCase() === 'paid' && invoice.paidat && (
                      <div className="flex items-center">
                        <CheckIcon className="h-5 w-5 text-green-500 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Paid Date</p>
                          <p className="text-sm text-green-600 font-medium">
                            {formatDate(invoice.paidat)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Amount Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Amount Details</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    {invoice.subtotal && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">Subtotal:</span>
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(invoice.subtotal)}
                        </span>
                      </div>
                    )}

                    {invoice.taxamount && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-gray-700">
                          Tax {invoice.taxrate ? `(${invoice.taxrate}%)` : ''}:
                        </span>
                        <span className="text-sm font-medium text-gray-900">
                          {formatCurrency(invoice.taxamount)}
                        </span>
                      </div>
                    )}

                    <div className="flex justify-between items-center border-t pt-3">
                      <span className="text-lg font-bold text-gray-900">Total:</span>
                      <span className="text-lg font-bold text-gray-900">
                        {formatCurrency(invoice.totalamount)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              {invoice.description && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {invoice.description}
                    </p>
                  </div>
                </div>
              )}

              {/* Status Information */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Status Information</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <StatusIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Current Status</p>
                        <p className="text-sm text-gray-600">{invoice.status}</p>
                      </div>
                    </div>
                    <div className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(invoice.status)}`}>
                      {invoice.status}
                    </div>
                  </div>

                  {isOverdue && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mr-2" />
                        <p className="text-sm text-red-700 font-medium">
                          This invoice is {Math.abs(daysUntilDue!)} days overdue
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t">
                <div className="text-sm text-gray-500">
                  Invoice #{invoice.id}
                </div>
                <div className="flex space-x-3">
                  <button className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    Download PDF
                  </button>
                  {invoice.status?.toLowerCase() !== 'paid' && (
                    <button className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                      Pay Now
                    </button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
