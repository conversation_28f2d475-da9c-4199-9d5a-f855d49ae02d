'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon, 
  CalendarIcon, 
  CurrencyDollarIcon, 
  ClockIcon,
  GlobeAltIcon,
  CodeBracketIcon,
  TagIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon
} from '@heroicons/react/24/outline'

interface Project {
  id: number
  name: string
  description: string
  status: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
}

interface ProjectViewModalProps {
  isOpen: boolean
  onClose: () => void
  project: Project
}

const getStatusColor = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'in progress':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'planning':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'on hold':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStatusIcon = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return CheckCircleIcon
    case 'in progress':
      return PlayIcon
    case 'planning':
      return ClockIcon
    case 'on hold':
      return ExclamationTriangleIcon
    case 'cancelled':
      return XMarkIcon
    default:
      return ClockIcon
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export default function ProjectViewModal({
  isOpen,
  onClose,
  project
}: ProjectViewModalProps) {
  if (!isOpen) return null

  const StatusIcon = getStatusIcon(project.status)
  const tags = project.tags ? project.tags.split(',').map(tag => tag.trim()) : []

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-white">Project Details</h2>
                  <p className="text-blue-100">{project.name}</p>
                </div>
                <div className="flex items-center space-x-3">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(project.status)}`}>
                    <StatusIcon className="h-4 w-4 inline mr-1" />
                    {project.status}
                  </div>
                  <button
                    type="button"
                    onClick={onClose}
                    className="text-white hover:text-gray-200 transition-colors"
                  >
                    <XMarkIcon className="h-6 w-6" />
                  </button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(95vh-80px)]">
              {/* Project Image */}
              {project.imageurl && (
                <div className="mb-6">
                  <img
                    src={project.imageurl}
                    alt={project.name}
                    className="w-full h-64 object-cover rounded-lg shadow-md"
                  />
                </div>
              )}

              {/* Project Information Grid */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                {/* Basic Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Project Information</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    <div className="flex items-center">
                      <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Start Date</p>
                        <p className="text-sm text-gray-600">
                          {project.projstartdate ? formatDate(project.projstartdate) : 'Not set'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Completion Date</p>
                        <p className="text-sm text-gray-600">
                          {project.projcompletiondate ? formatDate(project.projcompletiondate) : 'Not set'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <ClockIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Estimated Time</p>
                        <p className="text-sm text-gray-600">
                          {project.estimatetime || 'Not specified'}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">Estimated Cost</p>
                        <p className="text-sm text-gray-600">
                          {project.estimatecost ? formatCurrency(project.estimatecost) : 'Not specified'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Links and Resources */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Links & Resources</h3>
                  <div className="bg-gray-50 rounded-lg p-4 space-y-4">
                    {project.projecturl && (
                      <div className="flex items-center">
                        <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">Project URL</p>
                          <a
                            href={project.projecturl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 break-all"
                          >
                            {project.projecturl}
                          </a>
                        </div>
                      </div>
                    )}

                    {project.githuburl && (
                      <div className="flex items-center">
                        <CodeBracketIcon className="h-5 w-5 text-gray-400 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">GitHub Repository</p>
                          <a
                            href={project.githuburl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:text-blue-800 break-all"
                          >
                            {project.githuburl}
                          </a>
                        </div>
                      </div>
                    )}

                    {(!project.projecturl && !project.githuburl) && (
                      <div className="text-center py-4">
                        <p className="text-sm text-gray-500">No links available</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Description</h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <p className="text-gray-700 whitespace-pre-wrap">
                    {project.description || 'No description available'}
                  </p>
                </div>
              </div>

              {/* Tags */}
              {tags.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        <TagIcon className="h-3 w-3 mr-1" />
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t">
                <div className="text-sm text-gray-500">
                  Project ID: #{project.id}
                </div>
                <div className="flex space-x-3">
                  {project.projecturl && (
                    <a
                      href={project.projecturl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <GlobeAltIcon className="h-4 w-4 mr-2" />
                      View Project
                    </a>
                  )}
                  {project.githuburl && (
                    <a
                      href={project.githuburl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-800 hover:bg-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                    >
                      <CodeBracketIcon className="h-4 w-4 mr-2" />
                      View Code
                    </a>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  )
}
